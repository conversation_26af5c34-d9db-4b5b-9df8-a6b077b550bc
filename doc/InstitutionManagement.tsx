import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Badge } from "./ui/badge";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "./ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "./ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "./ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { Textarea } from "./ui/textarea";
import { Switch } from "./ui/switch";
import { 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  ArrowUpDown,
  Building2,
  Users,
  Clock,
  FileText,
  History,
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
  List,
  TreePine,
  LayoutGrid,
  GitBranch,
  Merge,
  Copy
} from "lucide-react";
import { InstitutionForm } from "./InstitutionForm";
import { InstitutionHistory } from "./InstitutionHistory";
import { InstitutionTreeView } from "./InstitutionTreeView";
import { InstitutionCodeRules } from "./InstitutionCodeRules";

export interface Institution {
  institutionID: string; // 机构ID (InstitutionID) PK
  institutionName: string; // 机构名称 (InstitutionName)
  institutionCode: string; // 机构编码 (InstitutionCode)
  institutionType: "permanent" | "temporary"; // 机构类型 (InstitutionType)
  parentInstitutionID: string | null; // 上级机构ID (ParentInstitutionID) FK
  establishDate: string; // 成立日期 (EstablishDate)
  effectiveDate: string; // 生效日期 (EffectiveDate)
  withdrawDate?: string; // 撤销日期 (WithdrawDate)
  reasonForChange?: string; // 撤销/合并原因 (ReasonForChange)
  solidifiedDataDescription?: string; // 固化数据说明 (SolidifiedDataDescription)
  description: string; // 职责描述 (Description)
  phoneNumber?: string; // 联系电话 (PhoneNumber)
  email?: string; // 邮箱 (Email)
  officeAddress?: string; // 办公地址 (OfficeAddress)
  status: "active" | "frozen" | "dissolved" | "merged"; // 状态 (Status)
  creatorID: string; // 创建人ID (CreatorID)
  createTime: string; // 创建时间 (CreateTime)
  lastModifierID: string; // 最后修改人ID (LastModifierID)
  updateTime: string; // 最后修改时间 (UpdateTime)
  approvalDocNumber?: string; // 批文号/依据 (ApprovalDocNumber)
  version: string; // 版本号 (Version)
  // 辅助字段
  level: number;
  employeeCount: number;
  parentName?: string;
}

export function InstitutionManagement() {
  const [institutions, setInstitutions] = useState<Institution[]>([
    {
      institutionID: "1",
      institutionName: "杭州科技职业技术学院",
      institutionCode: "HZVTC001",
      institutionType: "permanent",
      parentInstitutionID: null,
      establishDate: "2015-03-01",
      effectiveDate: "2015-03-01",
      description: "学院整体管理运营，负责全院教学、科研、管理工作的统筹协调",
      phoneNumber: "0571-88888888",
      email: "<EMAIL>",
      officeAddress: "杭州市西湖区留和路299号",
      status: "active",
      creatorID: "admin",
      createTime: "2015-03-01 09:00:00",
      lastModifierID: "admin",
      updateTime: "2024-01-15 10:30:00",
      approvalDocNumber: "浙教高〔2015〕10号",
      version: "1.0",
      level: 1,
      employeeCount: 1286,
      parentName: null
    },
    {
      institutionID: "2",
      institutionName: "计算机学院",
      institutionCode: "HZVTC002",
      institutionType: "permanent",
      parentInstitutionID: "1",
      establishDate: "2015-03-01",
      effectiveDate: "2015-03-01",
      description: "负责计算机科学与技术、软件工程、网络工程等专业的教学科研工作",
      phoneNumber: "0571-88888801",
      email: "<EMAIL>",
      officeAddress: "杭州市西湖区留和路299号计算机楼",
      status: "active",
      creatorID: "admin",
      createTime: "2015-03-01 09:00:00",
      lastModifierID: "user001",
      updateTime: "2024-01-10 14:20:00",
      approvalDocNumber: "浙教高〔2015〕10号",
      version: "1.2",
      level: 2,
      employeeCount: 156,
      parentName: "杭州科技职业技术学院"
    },
    {
      institutionID: "3",
      institutionName: "机械工程学院",
      institutionCode: "HZVTC003",
      institutionType: "permanent",
      parentInstitutionID: "1",
      establishDate: "2015-03-01",
      effectiveDate: "2015-03-01",
      description: "负责机械设计制造及其自动化、机械电子工程等专业的教学科研工作",
      phoneNumber: "0571-88888802",
      email: "<EMAIL>",
      officeAddress: "杭州市西湖区留和路299号机械楼",
      status: "active",
      creatorID: "admin",
      createTime: "2015-03-01 09:00:00",
      lastModifierID: "user002",
      updateTime: "2024-01-08 16:45:00",
      approvalDocNumber: "浙教高〔2015〕10号",
      version: "1.1",
      level: 2,
      employeeCount: 89,
      parentName: "杭州科技职业技术学院"
    },
    {
      institutionID: "4",
      institutionName: "AI技术创新项目组",
      institutionCode: "HZVTC004",
      institutionType: "temporary",
      parentInstitutionID: "2",
      establishDate: "2024-01-01",
      effectiveDate: "2024-01-01",
      withdrawDate: "2024-12-31",
      description: "负责人工智能技术研发与创新，推动AI技术在教学科研中的应用",
      phoneNumber: "0571-88888803",
      email: "<EMAIL>",
      officeAddress: "杭州市西湖区留和路299号计算机楼301",
      status: "active",
      creatorID: "user001",
      createTime: "2024-01-01 09:00:00",
      lastModifierID: "user001",
      updateTime: "2024-01-01 09:00:00",
      approvalDocNumber: "院发〔2024〕1号",
      version: "1.0",
      level: 3,
      employeeCount: 12,
      parentName: "计算机学院"
    },
    {
      institutionID: "5",
      institutionName: "软件工程系",
      institutionCode: "HZVTC005",
      institutionType: "permanent",
      parentInstitutionID: "2",
      establishDate: "2015-09-01",
      effectiveDate: "2015-09-01",
      description: "负责软件工程专业的教学管理、实践教学和产学研合作",
      phoneNumber: "0571-88888804",
      email: "<EMAIL>",
      officeAddress: "杭州市西湖区留和路299号计算机楼201",
      status: "active",
      creatorID: "user001",
      createTime: "2015-09-01 09:00:00",
      lastModifierID: "user003",
      updateTime: "2024-01-05 11:15:00",
      approvalDocNumber: "院发〔2015〕15号",
      version: "1.3",
      level: 3,
      employeeCount: 45,
      parentName: "计算机学院"
    },
    {
      institutionID: "6",
      institutionName: "网络技术系",
      institutionCode: "HZVTC006",
      institutionType: "permanent",
      parentInstitutionID: "2",
      establishDate: "2015-09-01",
      effectiveDate: "2015-09-01",
      description: "负责网络工程、计算机网络技术等专业的教学和技术服务",
      phoneNumber: "0571-88888805",
      email: "<EMAIL>",
      officeAddress: "杭州市西湖区留和路299号计算机楼202",
      status: "active",
      creatorID: "user001",
      createTime: "2015-09-01 09:00:00",
      lastModifierID: "user004",
      updateTime: "2024-01-03 09:30:00",
      approvalDocNumber: "院发〔2015〕15号",
      version: "1.2",
      level: 3,
      employeeCount: 38,
      parentName: "计算机学院"
    },
    {
      institutionID: "7",
      institutionName: "机械制造系",
      institutionCode: "HZVTC007",
      institutionType: "permanent",
      parentInstitutionID: "3",
      establishDate: "2015-09-01",
      effectiveDate: "2015-09-01",
      description: "负责机械制造与自动化、数控技术等专业的教学实训工作",
      phoneNumber: "0571-88888806",
      email: "<EMAIL>",
      officeAddress: "杭州市西湖区留和路299号机械楼101",
      status: "active",
      creatorID: "user002",
      createTime: "2015-09-01 09:00:00",
      lastModifierID: "user005",
      updateTime: "2024-01-02 15:20:00",
      approvalDocNumber: "院发〔2015〕16号",
      version: "1.1",
      level: 3,
      employeeCount: 32,
      parentName: "机械工程学院"
    },
    {
      institutionID: "8",
      institutionName: "自动化系",
      institutionCode: "HZVTC008",
      institutionType: "permanent",
      parentInstitutionID: "3",
      establishDate: "2015-09-01",
      effectiveDate: "2015-09-01",
      description: "负责电气自动化、工业机器人等专业的教学和技术开发",
      phoneNumber: "0571-88888807",
      email: "<EMAIL>",
      officeAddress: "杭州市西湖区留和路299号机械楼102",
      status: "active",
      creatorID: "user002",
      createTime: "2015-09-01 09:00:00",
      lastModifierID: "user006",
      updateTime: "2023-12-28 13:45:00",
      approvalDocNumber: "院发〔2015〕16号",
      version: "1.0",
      level: 3,
      employeeCount: 28,
      parentName: "机械工程学院"
    }
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [viewMode, setViewMode] = useState<"tree" | "table">("tree");
  const [selectedInstitution, setSelectedInstitution] = useState<Institution | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isCodeRulesOpen, setIsCodeRulesOpen] = useState(false);
  const [isTransferDialogOpen, setIsTransferDialogOpen] = useState(false);
  const [isMergeDialogOpen, setIsMergeDialogOpen] = useState(false);
  const [institutionToDelete, setInstitutionToDelete] = useState<Institution | null>(null);
  const [institutionToTransfer, setInstitutionToTransfer] = useState<Institution | null>(null);
  const [institutionToMerge, setInstitutionToMerge] = useState<Institution | null>(null);
  const [transferTargetId, setTransferTargetId] = useState<string>("");
  const [mergeTargetId, setMergeTargetId] = useState<string>("");
  const [transferReason, setTransferReason] = useState("");
  const [mergeReason, setMergeReason] = useState("");

  const filteredInstitutions = institutions.filter(institution => {
    const matchesSearch = institution.institutionName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         institution.institutionCode.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === "all" || institution.institutionType === filterType;
    const matchesStatus = filterStatus === "all" || institution.status === filterStatus;
    return matchesSearch && matchesType && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { label: "正常", variant: "default" as const, color: "bg-green-500" },
      frozen: { label: "冻结", variant: "secondary" as const, color: "bg-orange-500" },
      dissolved: { label: "已撤销", variant: "destructive" as const, color: "bg-red-500" },
      merged: { label: "已合并", variant: "outline" as const, color: "bg-gray-500" }
    };
    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <div className={`w-2 h-2 rounded-full ${config.color}`} />
        {config.label}
      </Badge>
    );
  };

  const getTypeBadge = (type: string) => {
    return type === "permanent" ? (
      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
        常设机构
      </Badge>
    ) : (
      <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
        临时组织
      </Badge>
    );
  };

  const handleEdit = (institution: Institution) => {
    setSelectedInstitution(institution);
    setIsFormOpen(true);
  };

  const handleDelete = (institution: Institution) => {
    setInstitutionToDelete(institution);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (institutionToDelete) {
      // 检查是否有下属机构或员工
      const hasSubInstitutions = institutions.some(inst => inst.parentInstitutionID === institutionToDelete.institutionID);
      
      if (hasSubInstitutions || institutionToDelete.employeeCount > 0) {
        alert("该机构下还有下属机构或在职员工，无法直接撤销。请先处理相关人员和机构。");
        return;
      }

      setInstitutions(prev => prev.map(inst => 
        inst.institutionID === institutionToDelete.institutionID 
          ? { ...inst, status: "dissolved" as const, withdrawDate: new Date().toISOString().split('T')[0] }
          : inst
      ));
      setIsDeleteDialogOpen(false);
      setInstitutionToDelete(null);
    }
  };

  const handleViewHistory = (institution: Institution) => {
    setSelectedInstitution(institution);
    setIsHistoryOpen(true);
  };

  // 划转操作
  const handleTransfer = (institution: Institution) => {
    setInstitutionToTransfer(institution);
    setTransferTargetId("");
    setTransferReason("");
    setIsTransferDialogOpen(true);
  };

  const confirmTransfer = () => {
    if (institutionToTransfer && transferTargetId) {
      // 前置校验
      const hasSubInstitutions = institutions.some(inst => inst.parentInstitutionID === institutionToTransfer.institutionID);
      if (hasSubInstitutions) {
        alert("该机构下还有下属机构，划转时将同时调整下属机构的层级关系。");
      }

      if (transferTargetId === "root") {
        // 设为根机构
        const updateHierarchy = (instId: string, newParentId: string | null, newLevel: number) => {
          const children = institutions.filter(inst => inst.parentInstitutionID === instId);
          setInstitutions(prev => prev.map(inst => {
            if (inst.institutionID === instId) {
              return {
                ...inst,
                parentInstitutionID: newParentId,
                parentName: newParentId ? undefined : null,
                level: newLevel,
                updateTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
                version: (parseFloat(inst.version) + 0.1).toFixed(1),
                reasonForChange: transferReason || "机构划转",
                solidifiedDataDescription: `原隶属关系: ${inst.parentName || "无"} → 根机构`
              };
            }
            return inst;
          }));

          // 递归更新子机构
          children.forEach(child => {
            updateHierarchy(child.institutionID, child.parentInstitutionID, newLevel + 1);
          });
        };

        updateHierarchy(institutionToTransfer.institutionID, null, 1);
      } else {
        const targetInstitution = institutions.find(inst => inst.institutionID === transferTargetId);
        if (targetInstitution) {
          // 更新机构层级
          const updateHierarchy = (instId: string, newParentId: string | null, newLevel: number) => {
            const children = institutions.filter(inst => inst.parentInstitutionID === instId);
            setInstitutions(prev => prev.map(inst => {
              if (inst.institutionID === instId) {
                return {
                  ...inst,
                  parentInstitutionID: newParentId,
                  parentName: targetInstitution.institutionName,
                  level: newLevel,
                  updateTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
                  version: (parseFloat(inst.version) + 0.1).toFixed(1),
                  reasonForChange: transferReason || "机构划转",
                  solidifiedDataDescription: `原隶属关系: ${inst.parentName || "无"} → ${targetInstitution.institutionName}`
                };
              }
              return inst;
            }));

            // 递归更新子机构
            children.forEach(child => {
              updateHierarchy(child.institutionID, child.parentInstitutionID, newLevel + 1);
            });
          };

          updateHierarchy(institutionToTransfer.institutionID, transferTargetId, targetInstitution.level + 1);
        }
      }

      setIsTransferDialogOpen(false);
      setInstitutionToTransfer(null);
      setTransferTargetId("");
      setTransferReason("");
    }
  };

  // 合并操作
  const handleMerge = (institution: Institution) => {
    setInstitutionToMerge(institution);
    setMergeTargetId("");
    setMergeReason("");
    setIsMergeDialogOpen(true);
  };

  const confirmMerge = () => {
    if (institutionToMerge && mergeTargetId) {
      // 前置校验
      const hasSubInstitutions = institutions.some(inst => inst.parentInstitutionID === institutionToMerge.institutionID);
      if (hasSubInstitutions) {
        alert("该机构下还有下属机构，请先处理下属机构的隶属关系。");
        return;
      }

      if (institutionToMerge.employeeCount > 0) {
        const confirmed = confirm(`该机构有 ${institutionToMerge.employeeCount} 名在职人员，合并后将自动迁移到目标机构。是否继续？`);
        if (!confirmed) return;
      }

      const targetInstitution = institutions.find(inst => inst.institutionID === mergeTargetId);
      if (targetInstitution) {
        setInstitutions(prev => prev.map(inst => {
          if (inst.institutionID === institutionToMerge.institutionID) {
            // 被合并的机构设为已合并状态
            return {
              ...inst,
              status: "merged" as const,
              withdrawDate: new Date().toISOString().split('T')[0],
              updateTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
              reasonForChange: mergeReason || "机构合并",
              solidifiedDataDescription: `已合并至: ${targetInstitution.institutionName} (${targetInstitution.institutionCode})`
            };
          }
          if (inst.institutionID === mergeTargetId) {
            // 目标机构人员数增加
            return {
              ...inst,
              employeeCount: inst.employeeCount + institutionToMerge.employeeCount,
              updateTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
              version: (parseFloat(inst.version) + 0.1).toFixed(1)
            };
          }
          return inst;
        }));
      }

      setIsMergeDialogOpen(false);
      setInstitutionToMerge(null);
      setMergeTargetId("");
      setMergeReason("");
    }
  };

  // 获取可选的划转目标机构（排除自己和下属机构）
  const getTransferTargets = (currentInstitution: Institution) => {
    return institutions.filter(inst => 
      inst.institutionID !== currentInstitution.institutionID &&
      inst.status === "active" &&
      !isDescendant(inst.institutionID, currentInstitution.institutionID)
    );
  };

  // 获取可选的合并目标机构（排除自己）
  const getMergeTargets = (currentInstitution: Institution) => {
    return institutions.filter(inst => 
      inst.institutionID !== currentInstitution.institutionID &&
      inst.status === "active" &&
      inst.institutionType === currentInstitution.institutionType // 同类型机构才能合并
    );
  };

  // 判断是否为子机构
  const isDescendant = (institutionId: string, ancestorId: string): boolean => {
    const inst = institutions.find(i => i.institutionID === institutionId);
    if (!inst || !inst.parentInstitutionID) return false;
    if (inst.parentInstitutionID === ancestorId) return true;
    return isDescendant(inst.parentInstitutionID, ancestorId);
  };

  return (
    <div className="space-y-6">
      {/* 页面标题和操作栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl">机构管理</h1>
          <p className="text-muted-foreground">
            管理学院组织架构，包括常设机构与临时组织的设立、修改、划转、撤销、合并等操作
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => {
              setSelectedInstitution(null);
              setIsFormOpen(true);
            }}
          >
            <Plus className="w-4 h-4 mr-2" />
            新增机构
          </Button>
          <Button 
            variant="outline"
            onClick={() => setIsCodeRulesOpen(true)}
          >
            <Settings className="w-4 h-4 mr-2" />
            编码规则配置
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">常设机构</p>
                <h3 className="text-2xl">
                  {institutions.filter(i => i.institutionType === "permanent" && i.status === "active").length}
                </h3>
              </div>
              <Building2 className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">临时组织</p>
                <h3 className="text-2xl">
                  {institutions.filter(i => i.institutionType === "temporary" && i.status === "active").length}
                </h3>
              </div>
              <Clock className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">总人员数</p>
                <h3 className="text-2xl">
                  {institutions.filter(i => i.status === "active").reduce((sum, i) => sum + i.employeeCount, 0)}
                </h3>
              </div>
              <Users className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">冻结/撤销</p>
                <h3 className="text-2xl">
                  {institutions.filter(i => i.status === "frozen" || i.status === "dissolved").length}
                </h3>
              </div>
              <AlertTriangle className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索机构名称或编码..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="机构类型" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部类型</SelectItem>
                <SelectItem value="permanent">常设机构</SelectItem>
                <SelectItem value="temporary">临时组织</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="状态筛选" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="active">正常</SelectItem>
                <SelectItem value="frozen">冻结</SelectItem>
                <SelectItem value="dissolved">已撤销</SelectItem>
                <SelectItem value="merged">已合并</SelectItem>
              </SelectContent>
            </Select>
            <div className="flex items-center gap-2 border rounded-lg p-1">
              <Button
                variant={viewMode === "tree" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("tree")}
                className="h-8"
              >
                <TreePine className="w-4 h-4 mr-1" />
                树形
              </Button>
              <Button
                variant={viewMode === "table" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("table")}
                className="h-8"
              >
                <List className="w-4 h-4 mr-1" />
                列表
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 机构展示区域 */}
      {viewMode === "tree" ? (
        <InstitutionTreeView
          institutions={filteredInstitutions}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onViewHistory={handleViewHistory}
          onTransfer={handleTransfer}
          onMerge={handleMerge}
        />
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <List className="w-5 h-5" />
              机构列表
            </CardTitle>
            <CardDescription>
              共 {filteredInstitutions.length} 个机构
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>机构编码</TableHead>
                  <TableHead>机构名称</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>上级机构</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>人员数</TableHead>
                  <TableHead>联系电话</TableHead>
                  <TableHead>成立日期</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInstitutions.map((institution) => (
                  <TableRow key={institution.institutionID}>
                    <TableCell className="font-mono">{institution.institutionCode}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-8 ${institution.level === 1 ? 'bg-blue-500' : 
                          institution.level === 2 ? 'bg-green-500' : 'bg-purple-500'}`} />
                        {institution.institutionName}
                      </div>
                    </TableCell>
                    <TableCell>{getTypeBadge(institution.institutionType)}</TableCell>
                    <TableCell>{institution.parentName || "-"}</TableCell>
                    <TableCell>{getStatusBadge(institution.status)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Users className="w-4 h-4 text-muted-foreground" />
                        {institution.employeeCount}
                      </div>
                    </TableCell>
                    <TableCell>{institution.phoneNumber || "-"}</TableCell>
                    <TableCell>{institution.establishDate}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>操作</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => handleEdit(institution)}>
                            <Edit className="w-4 h-4 mr-2" />
                            编辑
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleViewHistory(institution)}>
                            <History className="w-4 h-4 mr-2" />
                            查看历史
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          {institution.status === "active" && (
                            <>
                              <DropdownMenuItem onClick={() => handleTransfer(institution)}>
                                <GitBranch className="w-4 h-4 mr-2" />
                                划转
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleMerge(institution)}>
                                <Merge className="w-4 h-4 mr-2" />
                                合并
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handleDelete(institution)}>
                                <Trash2 className="w-4 h-4 mr-2" />
                                撤销
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* 新增/编辑机构对话框 */}
      <InstitutionForm
        open={isFormOpen}
        onOpenChange={setIsFormOpen}
        institution={selectedInstitution}
        institutions={institutions}
        onSave={(institution) => {
          if (selectedInstitution) {
            setInstitutions(prev => prev.map(inst => 
              inst.institutionID === selectedInstitution.institutionID ? { ...inst, ...institution } : inst
            ));
          } else {
            const newInstitution = {
              ...institution,
              institutionID: Date.now().toString(),
              updateTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
            };
            setInstitutions(prev => [...prev, newInstitution]);
          }
          setIsFormOpen(false);
          setSelectedInstitution(null);
        }}
      />

      {/* 历史记录对话框 */}
      <InstitutionHistory
        open={isHistoryOpen}
        onOpenChange={setIsHistoryOpen}
        institution={selectedInstitution}
      />

      {/* 编码规则配置对话框 */}
      <InstitutionCodeRules
        open={isCodeRulesOpen}
        onOpenChange={setIsCodeRulesOpen}
      />

      {/* 删除确认对话框 */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-orange-500" />
              确认撤销机构
            </DialogTitle>
            <DialogDescription>
              您确定要撤销机构 "{institutionToDelete?.institutionName}" 吗？此操作将：
              <ul className="mt-2 list-disc list-inside space-y-1">
                <li>将机构状态设置为"已撤销"</li>
                <li>设置撤销日期为当前日期</li>
                <li>从当前组织架构中移除</li>
                <li>保留历史数据以供查询</li>
              </ul>
              <p className="mt-2 text-orange-600 font-medium">
                注意：撤销前请确保机构下无在职人员和下属机构。
              </p>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              确认撤销
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 机构划转对话框 */}
      <Dialog open={isTransferDialogOpen} onOpenChange={setIsTransferDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <GitBranch className="w-5 h-5 text-blue-500" />
              机构划转
            </DialogTitle>
            <DialogDescription>
              调整机构的隶属关系，系统将自动更新组织层级并级联更新相关信息
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6 py-4">
            {/* 当前机构信息 */}
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="text-sm font-medium mb-2">当前机构信息</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">机构名称：</span>
                  <span className="font-medium">{institutionToTransfer?.institutionName}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">机构编码：</span>
                  <span className="font-mono">{institutionToTransfer?.institutionCode}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">当前上级：</span>
                  <span>{institutionToTransfer?.parentName || "无"}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">在职人员：</span>
                  <span>{institutionToTransfer?.employeeCount} 人</span>
                </div>
              </div>
            </div>

            {/* 划转目标选择 */}
            <div className="space-y-2">
              <Label htmlFor="transfer-target">选择新的上级机构 *</Label>
              <Select value={transferTargetId} onValueChange={setTransferTargetId}>
                <SelectTrigger>
                  <SelectValue placeholder="请选择新的上级机构" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="root">-- 设为根机构 --</SelectItem>
                  {institutionToTransfer && getTransferTargets(institutionToTransfer).map((inst) => (
                    <SelectItem key={inst.institutionID} value={inst.institutionID}>
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${inst.level === 1 ? 'bg-blue-500' : 
                          inst.level === 2 ? 'bg-green-500' : 'bg-purple-500'}`} />
                        <span>{inst.institutionName}</span>
                        <span className="text-muted-foreground">({inst.institutionCode})</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* 划转原因 */}
            <div className="space-y-2">
              <Label htmlFor="transfer-reason">划转原因</Label>
              <Textarea
                id="transfer-reason"
                placeholder="请输入划转原因（可选）"
                value={transferReason}
                onChange={(e) => setTransferReason(e.target.value)}
                rows={3}
              />
            </div>

            {/* 影响说明 */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h5 className="text-sm font-medium text-blue-800 mb-2">划转影响说明</h5>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• 机构层级关系将自动调整</li>
                <li>• 机构下所有人员的所属机构信息将同步更新</li>
                <li>• 子机构的层级关系将同时调整</li>
                <li>• 原隶属关系信息将进行数据固化保存</li>
              </ul>
            </div>

            {/* 前置检查结果 */}
            {institutionToTransfer && (
              <div className="border rounded-lg p-3">
                <h5 className="text-sm font-medium mb-2">前置检查</h5>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>在职人员：{institutionToTransfer.employeeCount} 人（将自动更新隶属关系）</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {institutions.some(inst => inst.parentInstitutionID === institutionToTransfer.institutionID) ? (
                      <>
                        <AlertTriangle className="w-4 h-4 text-orange-500" />
                        <span className="text-orange-700">
                          存在 {institutions.filter(inst => inst.parentInstitutionID === institutionToTransfer.institutionID).length} 个下属机构（将同时调整层级）
                        </span>
                      </>
                    ) : (
                      <>
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span>无下属机构</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsTransferDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={confirmTransfer} disabled={!transferTargetId}>
              <GitBranch className="w-4 h-4 mr-2" />
              确认划转
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 机构合并对话框 */}
      <Dialog open={isMergeDialogOpen} onOpenChange={setIsMergeDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Merge className="w-5 h-5 text-purple-500" />
              机构合并
            </DialogTitle>
            <DialogDescription>
              将当前机构合并到目标机构，人员和业务数据将无缝迁移
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6 py-4">
            {/* 当前机构信息 */}
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="text-sm font-medium mb-2">待合并机构信息</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">机构名称：</span>
                  <span className="font-medium">{institutionToMerge?.institutionName}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">机构编码：</span>
                  <span className="font-mono">{institutionToMerge?.institutionCode}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">机构类型：</span>
                  <span>{institutionToMerge?.institutionType === "permanent" ? "常设机构" : "临时组织"}</span>
                </div>
                <div>
                  <span className="text-muted-foreground">在职人员：</span>
                  <span className="font-medium text-orange-600">{institutionToMerge?.employeeCount} 人</span>
                </div>
              </div>
            </div>

            {/* 合并目标选择 */}
            <div className="space-y-2">
              <Label htmlFor="merge-target">选择目标机构 *</Label>
              <Select value={mergeTargetId} onValueChange={setMergeTargetId}>
                <SelectTrigger>
                  <SelectValue placeholder="请选择目标机构" />
                </SelectTrigger>
                <SelectContent>
                  {institutionToMerge && getMergeTargets(institutionToMerge).map((inst) => (
                    <SelectItem key={inst.institutionID} value={inst.institutionID}>
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${inst.level === 1 ? 'bg-blue-500' : 
                          inst.level === 2 ? 'bg-green-500' : 'bg-purple-500'}`} />
                        <span>{inst.institutionName}</span>
                        <span className="text-muted-foreground">({inst.institutionCode})</span>
                        <span className="text-xs text-muted-foreground">现有 {inst.employeeCount} 人</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                * 只能选择相同类型的机构进行合并
              </p>
            </div>

            {/* 合并原因 */}
            <div className="space-y-2">
              <Label htmlFor="merge-reason">合并原因 *</Label>
              <Textarea
                id="merge-reason"
                placeholder="请输入合并原因"
                value={mergeReason}
                onChange={(e) => setMergeReason(e.target.value)}
                rows={3}
                required
              />
            </div>

            {/* 合并效果预览 */}
            {mergeTargetId && institutionToMerge && (
              <div className="bg-purple-50 p-4 rounded-lg">
                <h5 className="text-sm font-medium text-purple-800 mb-2">合并效果预览</h5>
                {(() => {
                  const targetInst = institutions.find(inst => inst.institutionID === mergeTargetId);
                  return targetInst ? (
                    <div className="text-sm text-purple-700 space-y-1">
                      <p>• 目标机构：{targetInst.institutionName} ({targetInst.institutionCode})</p>
                      <p>• 人员合计：{targetInst.employeeCount} + {institutionToMerge.employeeCount} = {targetInst.employeeCount + institutionToMerge.employeeCount} 人</p>
                      <p>• "{institutionToMerge.institutionName}" 将设为"已合并"状态</p>
                      <p>• 所有历史数据将进行固化保存</p>
                    </div>
                  ) : null;
                })()}
              </div>
            )}

            {/* 前置检查结果 */}
            {institutionToMerge && (
              <div className="border rounded-lg p-3">
                <h5 className="text-sm font-medium mb-2">前置检查</h5>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    {institutions.some(inst => inst.parentInstitutionID === institutionToMerge.institutionID) ? (
                      <>
                        <XCircle className="w-4 h-4 text-red-500" />
                        <span className="text-red-700">
                          存在 {institutions.filter(inst => inst.parentInstitutionID === institutionToMerge.institutionID).length} 个下属机构，请先处理下属机构关系
                        </span>
                      </>
                    ) : (
                      <>
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span>无下属机构，可以合并</span>
                      </>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {institutionToMerge.employeeCount > 0 ? (
                      <>
                        <AlertTriangle className="w-4 h-4 text-orange-500" />
                        <span className="text-orange-700">{institutionToMerge.employeeCount} 名在职人员将迁移到目标机构</span>
                      </>
                    ) : (
                      <>
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span>无在职人员</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsMergeDialogOpen(false)}>
              取消
            </Button>
            <Button 
              onClick={confirmMerge} 
              disabled={!mergeTargetId || !mergeReason.trim() || 
                (institutionToMerge && institutions.some(inst => inst.parentInstitutionID === institutionToMerge.institutionID))}
            >
              <Merge className="w-4 h-4 mr-2" />
              确认合并
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}