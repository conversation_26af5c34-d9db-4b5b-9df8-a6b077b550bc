# Augment 使用规则（hky-hr-web）

本规则指导 Augment 在本项目中进行安全、规范、高效的自动化开发与验证。

## 技术与运行
- 框架：Vue 2.6（Options API） + Element UI 2.x
- 路由：vue-router（History 模式），真实运行以“后端菜单 + 动态路由”为准
- 状态：Vuex 模块化，自动注册
- 构建：Vue CLI 3（webpack 4）
- Node 兼容：Node ≥17 启动需加 `NODE_OPTIONS=--openssl-legacy-provider`；或使用 Node 14/16

## 安装与启动
- 安装：`npm install`
- 开发：
  - Node ≥17：`NODE_OPTIONS=--openssl-legacy-provider npm run dev`
  - Node 14/16：`npm run dev`
- 构建：`npm run build:prod` / `npm run build:stage`
- 质量：`npm run lint` / `npm run test:unit`

## 目录约定
- 入口：`src/main.js`
- 路由：
  - 静态：`src/router/routers.js`
  - 守卫/动态注入：`src/router/index.js`
- 状态：`src/store`（`modules/` 自动注册）
- API：`src/api`（统一基于 `@/utils/request`）
- 视图：`src/views/<模块>/...`
- 静态：`src/assets`
- 文档/示例：`doc/`（非构建产物，编辑器提示可忽略或在 jsconfig 中 exclude）

## 路由与菜单
- 上线场景：使用“后端返回菜单 + 动态路由”注入（`buildMenus` -> `filterAsyncRouter` -> `router.addRoutes`）
- 联调阶段：如需直达页面，可临时在 `src/router/routers.js` 添加静态路由；上线前移除

## API 规范
- 统一使用 `@/utils/request`（axios 实例）
- 在 `src/api/<模块>.js` 中模块化导出函数（list/get/add/edit/del 等），必要时扩展业务动作（如 transfer/merge）
- 遵循全局拦截器与异常提示规范

## Vuex 规范
- 新增模块文件置于 `src/store/modules/`，开启 `namespaced: true`
- 通过 action/mutation 进行状态管理，避免在组件直接修改全局状态

## 权限与字典
- 权限：组件中定义 `permission`，使用内置指令/工具校验（例如 add/edit/del 对应权限点）
- 字典：通过 `src/components/Dict` 能力注入

## UI 与样式
- Element UI 组件，默认 `size=small`
- 全局样式位于 `src/assets/styles/index.scss`，主题变量 `src/assets/styles/element-variables.scss`
- 图标：`src/assets/icons`（svg-sprite），新增图标建议 `npm run svgo`

## 代码修改规则
- 最小化增量变更：遵循“只改必须改的行”原则
- 修改现有文件：使用 `str_replace_editor`，单次编辑 ≤150 行
- 新建文件：使用 `save-file`，单文件 ≤300 行；超出需分批
- 严禁整文件覆盖或大范围无关重写
- 依赖管理：使用 `npm install/remove`，禁止直接手改 package.json 中 dependencies
- 执行命令：明确 cwd=仓库根目录；记录命令、输出与退出码

## 验证策略
- 安全校验优先：`npm run lint`、`npm run dev`、`npm run test:unit`
- 成功标准：退出码 0 且控制台无明显错误
- 失败处理：最小修复后重试；如涉及危险操作（写库、部署），先征求确认

## 编译错误自动修复（强制）
- 在任何代码修改后，若开发/构建命令（dev/build/test）出现错误，Augment 必须主动尝试自动修复。
- 修复优先级：
  1) 最小且安全的源码/配置微调（优先）
  2) 重跑相关命令验证结果；必要时多次迭代直至通过或明确受阻
  3) 仅当确属必须且已获许可时，才进行依赖的安装/升级/移除或构建链路大幅调整
- 每次尝试需记录：运行的命令、错误摘要、所做变更（最小 diff 思路）、再次验证结果。
- 若受权限/外部依赖/环境限制阻塞，应及时反馈并请求授权或指引。

## 展示代码片段
- 向用户展示本仓库代码时，务必用 `<augment_code_snippet>` XML 标签，并控制片段行数 <10 行

## 常见问题
- Node 20 运行 dev 报错 `ERR_OSSL_EVP_UNSUPPORTED`：使用 `NODE_OPTIONS=--openssl-legacy-provider` 或切换 Node 16
- `doc/` 下 TSX 示例导致编辑器提示：非构建产物，可在 `jsconfig.json` 中通过 `exclude` 排除

## 新页面通用流程（建议）
1) 视图与子组件：在 `src/views/<模块>/...` 创建入口与子组件
2) API 接入：在 `src/api/<模块>.js` 编写接口，组件内请求并渲染
3) 路由：联调阶段可临时静态路由，正式通过后端菜单注入
4) 权限：绑定按钮/操作权限点
5) 校验：lint+dev+必要的单测

