<template>
  <div class="page-wrapper">
    <!-- 筛选区 -->
    <el-card shadow="never" class="filter-card">
      <el-form :inline="true" :model="query" label-width="72px">
        <el-form-item label="年份：">
          <el-select v-model="query.year" placeholder="请选择" clearable style="width: 183px">
            <el-option
              v-for="y in yearOptions"
              :key="y"
              :label="y"
              :value="y"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建日期：">
          <el-date-picker
            v-model="query.dateRange"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 360px"
          />
        </el-form-item>
        <el-form-item label="标题：">
          <el-input v-model="query.title" placeholder="请输入" clearable style="width: 256px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 主体区：左侧分类 + 右侧表格 -->
    <div class="content">
      <!-- 左侧分类 -->
      <el-card shadow="never" class="sidebar">
        <div class="sidebar-search">
          <el-input v-model="categorySearch" placeholder="请搜索" clearable />
        </div>
        <div class="sidebar-section all" :class="{ active: activeCategory === '全部' }" @click="selectCategory('全部')">
          <span>全部</span>
        </div>
        <el-divider />
        <div class="sidebar-section">
          <div class="section-title">门类</div>
          <div class="section-item" v-for="c in filteredMainCategories" :key="c"
               :class="{ active: activeCategory === c }" @click="selectCategory(c)">
            <span>{{ c }}</span>
            <i class="el-icon-more"></i>
          </div>
        </div>
        <el-divider />
        <div class="sidebar-section">
          <div class="section-title">保管期限</div>
          <div class="section-item" v-for="t in retentionOptions" :key="t"
               :class="{ active: query.retention === t }" @click="selectRetention(t)">
            <span>{{ t }}</span>
          </div>
        </div>
      </el-card>

      <!-- 右侧表格 -->
      <el-card shadow="never" class="table-card">
        <div class="table-toolbar">
          <div class="left">
            <el-tag effect="plain">{{ activeCategory }}</el-tag>
            <template v-if="query.year">
              <el-tag type="info" effect="plain">年份：{{ query.year }}</el-tag>
            </template>
            <template v-if="query.retention">
              <el-tag type="success" effect="plain">期限：{{ query.retention }}</el-tag>
            </template>
          </div>
          <div class="right">
            <el-button type="primary" icon="el-icon-plus" plain>新增记录（示例）</el-button>
          </div>
        </div>
        <el-table :data="pagedData" border stripe size="small" v-loading="loading">
          <el-table-column type="index" label="#" width="60" />
          <el-table-column prop="title" label="标题" min-width="240" show-overflow-tooltip />
          <el-table-column prop="category" label="门类" width="120" />
          <el-table-column prop="year" label="年份" width="100" align="center" />
          <el-table-column prop="retention" label="保管期限" width="120" align="center" />
          <el-table-column prop="createdAt" label="创建日期" width="140" align="center" />
          <el-table-column label="操作" width="160" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="mini" @click="preview(row)">查看</el-button>
              <el-button type="text" size="mini" @click="edit(row)">编辑</el-button>
              <el-button type="text" size="mini" style="color:#F56C6C" @click="remove(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50]"
            :page-size.sync="page.size"
            :current-page.sync="page.current"
            :total="filteredData.length"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
// 模拟数据构造
const MAIN_CATEGORIES = ['文书', '收文', '基建', '会计', '人事', '照片', '录音', '录像', '业务数据', '公务电子邮箱', '网页信息', '社交媒体']
const RETENTION = ['10年', '30年', '永久']

function randomPick(arr) { return arr[Math.floor(Math.random() * arr.length)] }

function makeMockItems(count = 56) {
  const items = []
  const years = []
  const nowYear = new Date().getFullYear()
  for (let y = nowYear - 4; y <= nowYear + 1; y++) years.push(y)
  for (let i = 0; i < count; i++) {
    const year = randomPick(years)
    const month = String(1 + Math.floor(Math.random() * 12)).padStart(2, '0')
    const day = String(1 + Math.floor(Math.random() * 28)).padStart(2, '0')
    items.push({
      id: i + 1,
      title: `示例档案标题 ${i + 1}`,
      category: randomPick(MAIN_CATEGORIES),
      year,
      retention: randomPick(RETENTION),
      createdAt: `${year}-${month}-${day}`
    })
  }
  return items
}

export default {
  name: 'ArchiveManualList',
  data() {
    const now = new Date().getFullYear()
    return {
      loading: false,
      yearOptions: [now - 2, now - 1, now, now + 1, now + 2],
      query: {
        year: null,
        dateRange: null,
        title: '',
        retention: ''
      },
      categorySearch: '',
      activeCategory: '全部',
      retentionOptions: RETENTION,
      allData: makeMockItems(72),
      page: { current: 1, size: 10 }
    }
  },
  computed: {
    filteredMainCategories() {
      if (!this.categorySearch) return MAIN_CATEGORIES
      const q = this.categorySearch.trim().toLowerCase()
      return MAIN_CATEGORIES.filter(c => c.toLowerCase().includes(q))
    },
    filteredData() {
      const { year, dateRange, title } = this.query
      const [start, end] = dateRange || []
      const active = this.activeCategory
      const retention = this.query.retention
      return this.allData.filter(it => {
        if (active !== '全部' && it.category !== active) return false
        if (year && String(it.year) !== String(year)) return false
        if (retention && it.retention !== retention) return false
        if (title && !it.title.includes(title)) return false
        if (start && it.createdAt < start) return false
        if (end && it.createdAt > end) return false
        return true
      })
    },
    pagedData() {
      const start = (this.page.current - 1) * this.page.size
      return this.filteredData.slice(start, start + this.page.size)
    }
  },
  watch: {
    'page.size'() { this.page.current = 1 },
    query: {
      handler() { /* 保持分页合理 */ if (this.page.current > Math.ceil(this.filteredData.length / this.page.size)) this.page.current = 1 },
      deep: true
    }
  },
  methods: {
    selectCategory(c) {
      this.activeCategory = c
      this.page.current = 1
    },
    selectRetention(t) {
      this.query.retention = this.query.retention === t ? '' : t
      this.page.current = 1
    },
    handleSearch() {
      // 仅依赖计算属性过滤，无需额外逻辑
      this.page.current = 1
    },
    handleReset() {
      this.query = { year: null, dateRange: null, title: '', retention: '' }
      this.activeCategory = '全部'
      this.categorySearch = ''
      this.page.current = 1
    },
    preview(row) {
      this.$message.info(`查看：${row.title}`)
    },
    edit(row) {
      this.$message.success(`编辑：${row.title}`)
    },
    remove(row) {
      this.$confirm(`确认删除「${row.title}」吗？（示例，不会真正删除）`, '提示', { type: 'warning' }).then(() => {
        this.allData = this.allData.filter(it => it.id !== row.id)
        this.$message.success('已删除（示例）')
      }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.page-wrapper { display: flex; flex-direction: column; gap: 16px; }
.filter-card { padding-bottom: 8px; }
.content { display: grid; grid-template-columns: 220px 1fr; gap: 16px; }
.sidebar { padding: 0 0 8px 0; }
.sidebar-search { padding: 16px; border-bottom: 1px solid #DCDFE6; }
.sidebar-section { padding: 12px 16px; }
.sidebar-section.all { background: #F5F7FA; cursor: pointer; }
.section-title { color: #606266; font-weight: 500; padding: 0 16px 8px; }
.section-item { display: flex; align-items: center; justify-content: space-between; padding: 10px 16px; cursor: pointer; }
.section-item:hover { background: #F5F7FA; }
.section-item.active, .sidebar-section.all.active { background: #E3EDFF; border-left: 4px solid #2A78FF; padding-left: 12px; }
.table-card { min-height: 480px; }
.table-toolbar { display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px; }
.table-toolbar .left > * { margin-right: 8px; }
.pagination { display: flex; justify-content: flex-end; padding: 12px 0; }
</style>

