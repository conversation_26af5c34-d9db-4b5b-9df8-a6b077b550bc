<template>
  <div class="page-wrapper">
    <!-- 筛选区 -->
    <el-card shadow="never" class="filter-card">
      <el-form :inline="true" :model="query" label-width="72px">
        <el-form-item label="年份：">
          <el-select v-model="query.year" placeholder="请选择" clearable style="width: 183px">
            <el-option
              v-for="y in yearOptions"
              :key="y"
              :label="y"
              :value="y"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建日期：">
          <el-date-picker
            v-model="query.dateRange"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            style="width: 360px"
          />
        </el-form-item>
        <el-form-item label="标题：">
          <el-input v-model="query.title" placeholder="请输入" clearable style="width: 256px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 主体区：左侧分类 + 右侧表格 -->
    <div class="content">
      <!-- 左侧目录树 -->
      <el-card shadow="never" class="sidebar">
        <div class="sidebar-search">
          <el-input v-model="treeFilter" placeholder="请搜索" clearable @input="onTreeFilterChange" />
        </div>
        <el-tree
          ref="tree"
          class="dir-tree"
          :data="treeData"
          node-key="id"
          :props="treeProps"
          :filter-node-method="filterTreeNode"
          highlight-current
          default-expand-all
          @node-click="onTreeNodeClick"
        />
      </el-card>

      <!-- 右侧表格 -->
      <el-card shadow="never" class="table-card">
        <div class="table-toolbar">
          <div class="left">
            <template v-if="query.category">
              <el-tag effect="plain">门类：{{ query.category }}</el-tag>
            </template>
            <template v-if="query.year">
              <el-tag type="info" effect="plain">年份：{{ query.year }}</el-tag>
            </template>
            <template v-if="query.retention">
              <el-tag type="success" effect="plain">保管期限：{{ query.retention }}</el-tag>
            </template>
          </div>
        </div>
        <el-table :data="pagedData" border stripe size="small" v-loading="loading">
          <el-table-column prop="title" label="标题" min-width="320" show-overflow-tooltip />
          <el-table-column prop="category" label="门类" width="120" />
          <el-table-column prop="retention" label="保管期限" width="100" align="center" />
          <el-table-column prop="createdAt" label="创建日期" width="140" align="center" />
          <el-table-column prop="year" label="年份" width="90" align="center" />
        </el-table>
        <div class="pagination">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50]"
            :page-size.sync="page.size"
            :current-page.sync="page.current"
            :total="filteredData.length"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
// 模拟数据构造
const MAIN_CATEGORIES = ['文书', '收文', '基建', '会计', '人事', '照片', '录音', '录像', '业务数据', '公务电子邮箱', '网页信息', '社交媒体']
const RETENTION = ['10年', '30年', '永久']

function randomPick(arr) { return arr[Math.floor(Math.random() * arr.length)] }

function makeMockItems(count = 56) {
  const items = []
  const years = []
  const nowYear = new Date().getFullYear()
  for (let y = nowYear - 4; y <= nowYear + 1; y++) years.push(y)
  for (let i = 0; i < count; i++) {
    const year = randomPick(years)
    const month = String(1 + Math.floor(Math.random() * 12)).padStart(2, '0')
    const day = String(1 + Math.floor(Math.random() * 28)).padStart(2, '0')
    items.push({
      id: i + 1,
      title: `示例档案标题 ${i + 1}`,
      category: randomPick(MAIN_CATEGORIES),
      year,
      retention: randomPick(RETENTION),
      createdAt: `${year}-${month}-${day}`
    })
  }
  return items
}

export default {
  name: 'ArchiveManualList',
  data() {
    const now = new Date().getFullYear()
    return {
      loading: false,
      yearOptions: [now - 2, now - 1, now, now + 1, now + 2],
      query: {
        year: null,
        dateRange: null,
        title: '',
        retention: '',
        category: ''
      },
      // 左侧目录树
      treeFilter: '',
      treeData: [],
      treeProps: { children: 'children', label: 'label' },
      retentionOptions: RETENTION,
      allData: makeMockItems(72),
      page: { current: 1, size: 10 }
    }
  },
  computed: {
    filteredData() {
      const { year, dateRange, title, category } = this.query
      const [start, end] = dateRange || []
      const retention = this.query.retention
      return this.allData.filter(it => {
        if (category && it.category !== category) return false
        if (year && String(it.year) !== String(year)) return false
        if (retention && it.retention !== retention) return false
        if (title && !it.title.includes(title)) return false
        if (start && it.createdAt < start) return false
        if (end && it.createdAt > end) return false
        return true
      })
    },
    pagedData() {
      const start = (this.page.current - 1) * this.page.size
      return this.filteredData.slice(start, start + this.page.size)
    }
  },
  watch: {
    'page.size'() { this.page.current = 1 },
    query: {
      handler() { if (this.page.current > Math.ceil(this.filteredData.length / this.page.size)) this.page.current = 1 },
      deep: true
    }
  },
  methods: {
    // 目录树相关
    onTreeFilterChange() {
      this.$refs.tree && this.$refs.tree.filter(this.treeFilter)
    },
    filterTreeNode(value, data) {
      if (!value) return true
      return data.label.toLowerCase().includes(value.toLowerCase())
    },
    onTreeNodeClick(node) {
      // 叶子节点视为具体门类；非叶节点点击只展开不筛选
      if (!node.children || node.children.length === 0) {
        this.query.category = node.label
        this.page.current = 1
      }
    },
    selectRetention(t) {
      this.query.retention = this.query.retention === t ? '' : t
      this.page.current = 1
    },
    handleSearch() {
      this.page.current = 1
    },
    handleReset() {
      this.query = { year: null, dateRange: null, title: '', retention: '', category: '' }
      this.treeFilter = ''
      this.$nextTick(() => this.$refs.tree && this.$refs.tree.setCurrentKey(null))
      this.page.current = 1
    },
    preview(row) {
      this.$message.info(`查看：${row.title}`)
    }
  },
  created() {
    // 构造目录树：与原型一致的层级示意（分类组 -> 门类列表）
    this.treeData = [
      {
        id: 'cat-1', label: '文书', children: [
          { id: 'cat-1-2022', label: '2022' },
          { id: 'cat-1-2023', label: '2023' },
          { id: 'cat-1-2024', label: '2024' }
        ]
      },
      { id: 'cat-2', label: '收文' },
      { id: 'cat-3', label: '基建' },
      { id: 'cat-4', label: '会计' },
      { id: 'cat-5', label: '人事' },
      { id: 'cat-6', label: '照片' },
      { id: 'cat-7', label: '录音' },
      { id: 'cat-8', label: '录像' },
      { id: 'cat-9', label: '业务数据' },
      { id: 'cat-10', label: '公务电子邮箱' },
      { id: 'cat-11', label: '网页信息' },
      { id: 'cat-12', label: '社交媒体' }
    ]
  }
}
</script>

<style scoped>
.page-wrapper { display: flex; flex-direction: column; gap: 16px; }
.filter-card { padding-bottom: 8px; }
.content { display: grid; grid-template-columns: 220px 1fr; gap: 16px; }
.sidebar { padding: 0 0 8px 0; }
.sidebar-search { padding: 16px; border-bottom: 1px solid #DCDFE6; }
.sidebar-section { padding: 12px 16px; }
.sidebar-section.all { background: #F5F7FA; cursor: pointer; }
.section-title { color: #606266; font-weight: 500; padding: 0 16px 8px; }
.section-item { display: flex; align-items: center; justify-content: space-between; padding: 10px 16px; cursor: pointer; }
.section-item:hover { background: #F5F7FA; }
.section-item.active, .sidebar-section.all.active { background: #E3EDFF; border-left: 4px solid #2A78FF; padding-left: 12px; }
.table-card { min-height: 480px; }
.table-toolbar { display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px; }
.table-toolbar .left > * { margin-right: 8px; }
.pagination { display: flex; justify-content: flex-end; padding: 12px 0; }
</style>

