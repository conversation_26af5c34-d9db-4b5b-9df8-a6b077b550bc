<template>
  <el-dialog :visible.sync="visible" title="机构历史" width="720px">
    <div v-if="institution">
      <el-timeline>
        <el-timeline-item
          v-for="(h, idx) in history"
          :key="idx"
          :timestamp="h.time"
          :type="h.type"
        >
          <p class="title">{{ h.title }}</p>
          <p class="desc">{{ h.desc }}</p>
        </el-timeline-item>
      </el-timeline>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="visible=false">关 闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'InstitutionHistory',
  props: {
    value: { type: Boolean, default: false },
    institution: { type: Object, default: null }
  },
  data() {
    return { visible: this.value }
  },
  computed: {
    history() {
      if (!this.institution) return []
      // 使用存在的字段拼装简单历史示例（真实项目应请求后端）
      return [
        { time: this.institution.createTime || '—', type: 'primary', title: '创建机构', desc: `创建人: ${this.institution.creatorID || '—'}` },
        { time: this.institution.updateTime || '—', type: 'success', title: '最近更新', desc: `更新人: ${this.institution.lastModifierID || '—'} 版本: ${this.institution.version || '—'}` },
        this.institution.withdrawDate ? { time: this.institution.withdrawDate, type: 'danger', title: '撤销/合并', desc: this.institution.reasonForChange || '—' } : null
      ].filter(Boolean)
    }
  },
  watch: {
    value(v) { this.visible = v },
    visible(v) { this.$emit('input', v) }
  }
}
</script>

<style scoped>
.title { font-weight: 600; }
.desc { color: #606266; }
</style>

