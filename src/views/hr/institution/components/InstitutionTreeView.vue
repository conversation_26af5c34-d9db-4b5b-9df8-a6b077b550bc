<template>
  <el-card shadow="never">
    <div slot="header" class="clearfix">
      <span>机构树</span>
    </div>
    <el-tree
      :data="treeData"
      :props="defaultProps"
      node-key="institutionID"
      default-expand-all
      highlight-current
      :expand-on-click-node="false"
    >
      <span slot-scope="{ node, data }" class="custom-tree-node">
        <span class="node-left">
          <i :class="levelColor(data.level)" class="level-bar" />
          <span class="name">{{ data.institutionName }}</span>
          <el-tag size="mini" type="info" effect="plain" class="ml8">{{ data.institutionCode }}</el-tag>
          <el-tag size="mini" :type="data.institutionType === 'permanent' ? 'success' : 'warning'" effect="plain" class="ml4">
            {{ data.institutionType === 'permanent' ? '常设' : '临时' }}
          </el-tag>
          <el-tag size="mini" :type="statusTagType(data.status)" effect="plain" class="ml4">{{ statusLabel(data.status) }}</el-tag>
        </span>
        <span class="actions">
          <el-button type="text" size="mini" @click="$emit('edit', data)">编辑</el-button>
          <el-button type="text" size="mini" @click="$emit('history', data)">历史</el-button>
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              更多<i class="el-icon-arrow-down el-icon--right" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="$emit('transfer', data)">划转</el-dropdown-item>
              <el-dropdown-item @click.native="$emit('merge', data)">合并</el-dropdown-item>
              <el-dropdown-item v-if="data.status==='active'" divided @click.native="$emit('delete', data)">撤销</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </span>
      </span>
    </el-tree>
  </el-card>
</template>

<script>
export default {
  name: 'InstitutionTreeView',
  props: {
    institutions: { type: Array, required: true }
  },
  computed: {
    treeData() {
      // 构建树（基于传入的已过滤列表）
      const map = {}
      this.institutions.forEach(i => { map[i.institutionID] = { ...i, children: [] } })
      const roots = []
      Object.values(map).forEach(node => {
        if (node.parentInstitutionID && map[node.parentInstitutionID]) {
          map[node.parentInstitutionID].children.push(node)
        } else {
          roots.push(node)
        }
      })
      return roots
    },
    defaultProps() {
      return { children: 'children', label: 'institutionName' }
    }
  },
  methods: {
    levelColor(level) {
      return level === 1 ? 'blue' : level === 2 ? 'green' : 'purple'
    },
    statusTagType(s) {
      if (s === 'active') return 'success'
      if (s === 'frozen') return 'warning'
      if (s === 'dissolved') return 'danger'
      return ''
    },
    statusLabel(s) {
      const m = { active: '正常', frozen: '冻结', dissolved: '已撤销', merged: '已合并' }
      return m[s] || s
    }
  }
}
</script>

<style scoped>
.custom-tree-node { display: flex; flex: 1; align-items: center; justify-content: space-between; padding: 4px 8px; }
.node-left { display: inline-flex; align-items: center; }
.level-bar { width: 4px; height: 16px; margin-right: 8px; border-radius: 2px; background: #909399; }
.blue.level-bar { background: #409EFF; }
.green.level-bar { background: #67C23A; }
.purple.level-bar { background: #A66EFB; }
.ml4 { margin-left: 4px; }
.ml8 { margin-left: 8px; }
.actions { display: inline-flex; gap: 4px; align-items: center; }
</style>

