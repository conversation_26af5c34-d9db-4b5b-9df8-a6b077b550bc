<template>
  <el-dialog :visible.sync="visible" :title="form.id ? '编辑机构' : '新增机构'" width="640px">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="机构名称" prop="institutionName">
            <el-input v-model="form.institutionName" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="机构编码" prop="institutionCode">
            <el-input v-model="form.institutionCode" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="机构类型" prop="institutionType">
            <el-select v-model="form.institutionType" placeholder="请选择">
              <el-option label="常设机构" value="permanent" />
              <el-option label="临时组织" value="temporary" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上级机构">
            <el-select v-model="form.parentInstitutionID" clearable filterable placeholder="选择上级">
              <el-option :label="'-- 根机构 --'" :value="null" />
              <el-option v-for="i in institutions" :key="i.institutionID" :label="i.institutionName" :value="i.institutionID" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="成立日期" prop="establishDate">
            <el-date-picker v-model="form.establishDate" type="date" value-format="yyyy-MM-dd" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="生效日期" prop="effectiveDate">
            <el-date-picker v-model="form.effectiveDate" type="date" value-format="yyyy-MM-dd" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话">
            <el-input v-model="form.phoneNumber" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱">
            <el-input v-model="form.email" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="办公地址">
            <el-input v-model="form.officeAddress" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="职责描述" prop="description">
            <el-input v-model="form.description" type="textarea" :rows="3" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="submit">保 存</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'InstitutionForm',
  props: {
    value: { type: Boolean, default: false },
    institution: { type: Object, default: null },
    institutions: { type: Array, default: () => [] }
  },
  data() {
    return {
      visible: this.value,
      form: this.initForm(this.institution),
      rules: {
        institutionName: [{ required: true, message: '请输入机构名称', trigger: 'blur' }],
        institutionCode: [{ required: true, message: '请输入机构编码', trigger: 'blur' }],
        institutionType: [{ required: true, message: '请选择机构类型', trigger: 'change' }],
        establishDate: [{ required: true, message: '请选择成立日期', trigger: 'change' }],
        effectiveDate: [{ required: true, message: '请选择生效日期', trigger: 'change' }],
        description: [{ required: true, message: '请输入职责描述', trigger: 'blur' }]
      }
    }
  },
  watch: {
    value(v) { this.visible = v },
    visible(v) { this.$emit('input', v) },
    institution: {
      handler(v) { this.form = this.initForm(v) },
      deep: true
    }
  },
  methods: {
    initForm(v) {
      return v ? { ...v } : {
        id: null,
        institutionName: '',
        institutionCode: '',
        institutionType: 'permanent',
        parentInstitutionID: null,
        establishDate: '',
        effectiveDate: '',
        description: '',
        phoneNumber: '',
        email: '',
        officeAddress: ''
      }
    },
    close() { this.visible = false },
    submit() {
      this.$refs.form.validate(valid => {
        if (!valid) return
        const payload = { ...this.form }
        this.$emit('save', payload)
      })
    }
  }
}
</script>

<style scoped>
</style>

