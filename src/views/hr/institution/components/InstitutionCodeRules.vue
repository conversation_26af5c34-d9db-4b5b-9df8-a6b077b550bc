<template>
  <el-dialog :visible.sync="visible" title="编码规则配置" width="640px">
    <el-form :model="form" label-width="120px">
      <el-form-item label="编码前缀">
        <el-input v-model="form.prefix" placeholder="如 HZVTC" />
      </el-form-item>
      <el-form-item label="位数">
        <el-input-number v-model="form.length" :min="2" :max="12" />
      </el-form-item>
      <el-form-item label="是否包含年份">
        <el-switch v-model="form.includeYear" />
      </el-form-item>
      <el-form-item label="示例">
        <el-alert :title="example" type="info" :closable="false" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible=false">取 消</el-button>
      <el-button type="primary" @click="$emit('save', form); visible=false">保 存</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'InstitutionCodeRules',
  props: { value: { type: Boolean, default: false }},
  data() {
    return {
      visible: this.value,
      form: { prefix: 'HZVTC', length: 3, includeYear: false }
    }
  },
  computed: {
    example() {
      const y = this.form.includeYear ? new Date().getFullYear() : ''
      return `${this.form.prefix}${y}${'0'.repeat(this.form.length)}1`
    }
  },
  watch: { value(v) { this.visible = v }, visible(v) { this.$emit('input', v) } }
}
</script>

<style scoped>
</style>

