<template>
  <div class="institution-page">
    <!-- 标题与操作 -->
    <div class="page-header">
      <div>
        <h2>机构管理</h2>
        <p class="sub">管理组织架构，包括常设机构与临时组织的设立、修改、划转、撤销、合并等操作</p>
      </div>
      <div class="actions">
        <el-button size="small" @click="openCreate"><i class="el-icon-plus" /> 新增机构</el-button>
        <el-button size="small" @click="codeRulesVisible = true"><i class="el-icon-setting" /> 编码规则配置</el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="16" class="mb16">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="never">
          <div class="stat">
            <div>
              <p class="muted">常设机构</p>
              <h3>{{ activePermanentCount }}</h3>
            </div>
            <i class="el-icon-office-building icon blue" />
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="never">
          <div class="stat">
            <div>
              <p class="muted">临时组织</p>
              <h3>{{ activeTemporaryCount }}</h3>
            </div>
            <i class="el-icon-time icon purple" />
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="never">
          <div class="stat">
            <div>
              <p class="muted">总人员数</p>
              <h3>{{ totalEmployees }}</h3>
            </div>
            <i class="el-icon-user icon green" />
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6">
        <el-card shadow="never">
          <div class="stat">
            <div>
              <p class="muted">冻结/撤销</p>
              <h3>{{ alertCount }}</h3>
            </div>
            <i class="el-icon-warning icon orange" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 搜索筛选与视图切换 -->
    <el-card shadow="never" class="mb16">
      <el-row :gutter="16" type="flex" align="middle">
        <el-col :xs="24" :sm="10" class="mb8-sm">
          <el-input v-model="searchTerm" placeholder="搜索机构名称或编码..." clearable>
            <i slot="prefix" class="el-input__icon el-icon-search" />
          </el-input>
        </el-col>
        <el-col :xs="12" :sm="4" class="mb8-sm">
          <el-select v-model="filterType" placeholder="机构类型">
            <el-option label="全部类型" value="all" />
            <el-option label="常设机构" value="permanent" />
            <el-option label="临时组织" value="temporary" />
          </el-select>
        </el-col>
        <el-col :xs="12" :sm="4" class="mb8-sm">
          <el-select v-model="filterStatus" placeholder="状态">
            <el-option label="全部状态" value="all" />
            <el-option label="正常" value="active" />
            <el-option label="冻结" value="frozen" />
            <el-option label="已撤销" value="dissolved" />
            <el-option label="已合并" value="merged" />
          </el-select>
        </el-col>
        <el-col :xs="24" :sm="6" class="view-toggle mt8-sm">
          <el-button-group>
            <el-button size="small" :type="viewMode==='tree'?'primary':'default'" @click="viewMode='tree'">树形</el-button>
            <el-button size="small" :type="viewMode==='table'?'primary':'default'" @click="viewMode='table'">列表</el-button>
          </el-button-group>
        </el-col>
      </el-row>
    </el-card>

    <!-- 展示区域：树/表 -->
    <institution-tree-view
      v-if="viewMode==='tree'"
      :institutions="filteredInstitutions"
      @edit="onEdit"
      @delete="onDelete"
      @history="onViewHistory"
      @transfer="onTransfer"
      @merge="onMerge"
    />

    <el-card v-else shadow="never">
      <div slot="header" class="card-header">
        <span>机构列表</span>
        <span class="muted count">（共 {{ filteredInstitutions.length }} 个）</span>
      </div>
      <el-table :data="filteredInstitutions" size="small" border :stripe="true">
        <el-table-column prop="institutionCode" label="机构编码" width="140">
          <template slot-scope="scope"><span class="mono">{{ scope.row.institutionCode }}</span></template>
        </el-table-column>
        <el-table-column label="机构名称" min-width="220">
          <template slot-scope="scope">
            <span class="level-bar" :class="levelColor(scope.row.level)" />
            {{ scope.row.institutionName }}
          </template>
        </el-table-column>
        <el-table-column label="类型" width="100">
          <template slot-scope="scope">
            <el-tag size="mini" :type="scope.row.institutionType==='permanent'?'success':'warning'" effect="plain">
              {{ scope.row.institutionType==='permanent'?'常设机构':'临时组织' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="parentName" label="上级机构" min-width="140" />
        <el-table-column label="状态" width="110">
          <template slot-scope="scope">
            <el-tag size="mini" :type="statusTagType(scope.row.status)" effect="plain">{{ statusLabel(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="人员数" width="100">
          <template slot-scope="scope">
            <i class="el-icon-user" /> {{ scope.row.employeeCount }}
          </template>
        </el-table-column>
        <el-table-column prop="phoneNumber" label="联系电话" width="140" />
        <el-table-column prop="establishDate" label="成立日期" width="120" />
        <el-table-column label="操作" width="180" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="onEdit(scope.row)">编辑</el-button>
            <el-button type="text" size="mini" @click="onViewHistory(scope.row)">历史</el-button>
            <el-dropdown trigger="click">
              <span class="el-dropdown-link">更多<i class="el-icon-arrow-down el-icon--right" /></span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="onTransfer(scope.row)">划转</el-dropdown-item>
                <el-dropdown-item @click.native="onMerge(scope.row)">合并</el-dropdown-item>
                <el-dropdown-item v-if="scope.row.status==='active'" divided @click.native="onDelete(scope.row)">撤销</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 子组件对话框 -->
    <institution-form
      v-model="formVisible"
      :institution="selectedInstitution"
      :institutions="institutions"
      @save="onSave"
    />
    <institution-history v-model="historyVisible" :institution="selectedInstitution" />
    <institution-code-rules v-model="codeRulesVisible" @save="onSaveCodeRule" />

    <!-- 撤销确认 -->
    <el-dialog :visible.sync="deleteVisible" title="确认撤销机构" width="520px">
      <div>
        您确定要撤销机构 “<b>{{ toDelete && toDelete.institutionName }}</b>” 吗？
        <ul class="mt8">
          <li>将机构状态设置为“已撤销”，并设置撤销日期为当前日期</li>
          <li>从当前组织架构中移除，但保留历史数据</li>
          <li class="orange">注意：撤销前请确保机构下无在职人员和下属机构</li>
        </ul>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteVisible=false">取 消</el-button>
        <el-button type="danger" @click="confirmDelete">确认撤销</el-button>
      </span>
    </el-dialog>

    <!-- 划转对话框 -->
    <el-dialog :visible.sync="transferVisible" title="机构划转" width="720px">
      <div v-if="toTransfer" class="hint">
        <div class="row"><span class="muted">机构名称：</span><b>{{ toTransfer.institutionName }}</b></div>
        <div class="row"><span class="muted">机构编码：</span><span class="mono">{{ toTransfer.institutionCode }}</span></div>
        <div class="row"><span class="muted">当前上级：</span>{{ toTransfer.parentName || '无' }}</div>
        <div class="row"><span class="muted">在职人员：</span>{{ toTransfer.employeeCount }} 人</div>
      </div>
      <el-form label-width="110px">
        <el-form-item label="新的上级机构">
          <el-select v-model="transferTargetId" filterable placeholder="请选择">
            <el-option label="-- 设为根机构 --" value="root" />
            <el-option v-for="i in transferTargets" :key="i.institutionID" :label="i.institutionName + ' (' + i.institutionCode + ')'" :value="i.institutionID" />
          </el-select>
        </el-form-item>
        <el-form-item label="划转原因">
          <el-input v-model="transferReason" type="textarea" :rows="3" placeholder="请输入划转原因（可选）" />
        </el-form-item>
      </el-form>
      <el-alert
        title="• 机构层级关系及其子机构将自动调整；• 人员所属机构信息将同步更新；• 原隶属关系将进行数据固化保存"
        type="info"
        :closable="false"
        show-icon
        class="mt8"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="transferVisible=false">取 消</el-button>
        <el-button type="primary" :disabled="!transferTargetId" @click="confirmTransfer">确认划转</el-button>
      </span>
    </el-dialog>

    <!-- 合并对话框 -->
    <el-dialog :visible.sync="mergeVisible" title="机构合并" width="720px">
      <div v-if="toMerge" class="hint">
        <div class="row"><span class="muted">机构名称：</span><b>{{ toMerge.institutionName }}</b></div>
        <div class="row"><span class="muted">机构编码：</span><span class="mono">{{ toMerge.institutionCode }}</span></div>
        <div class="row"><span class="muted">机构类型：</span>{{ toMerge.institutionType==='permanent'?'常设机构':'临时组织' }}</div>
        <div class="row"><span class="muted">在职人员：</span><span class="orange">{{ toMerge.employeeCount }} 人</span></div>
      </div>
      <el-form label-width="110px">
        <el-form-item label="目标机构">
          <el-select v-model="mergeTargetId" filterable placeholder="请选择">
            <el-option v-for="i in mergeTargets" :key="i.institutionID" :label="i.institutionName + ' (' + i.institutionCode + ')'" :value="i.institutionID" />
          </el-select>
          <div class="muted text-xs">只能选择相同类型的机构进行合并</div>
        </el-form-item>
        <el-form-item label="合并原因">
          <el-input v-model="mergeReason" type="textarea" :rows="3" placeholder="请输入合并原因" />
        </el-form-item>
      </el-form>
      <el-alert
        v-if="mergePreview"
        :title="mergePreview"
        type="warning"
        :closable="false"
        show-icon
        class="mt8"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="mergeVisible=false">取 消</el-button>
        <el-button type="primary" :disabled="!mergeTargetId || !mergeReason.trim() || hasSubOfMerge" @click="confirmMerge">确认合并</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import InstitutionTreeView from './components/InstitutionTreeView.vue'
import InstitutionForm from './components/InstitutionForm.vue'
import InstitutionHistory from './components/InstitutionHistory.vue'
import InstitutionCodeRules from './components/InstitutionCodeRules.vue'
import instApi from '@/api/hr/institution'

export default {
  name: 'HrInstitution',
  components: { InstitutionTreeView, InstitutionForm, InstitutionHistory, InstitutionCodeRules },
  data() {
    return {
      institutions: [],
      searchTerm: '',
      filterType: 'all',
      filterStatus: 'all',
      viewMode: 'tree',
      // dialogs
      formVisible: false,
      historyVisible: false,
      codeRulesVisible: false,
      deleteVisible: false,
      transferVisible: false,
      mergeVisible: false,
      // selection
      selectedInstitution: null,
      toDelete: null,
      toTransfer: null,
      toMerge: null,
      transferTargetId: '',
      mergeTargetId: '',
      transferReason: '',
      mergeReason: ''
    }
  },
  computed: {
    filteredInstitutions() {
      return this.institutions.filter(i => {
        const s = this.searchTerm.trim().toLowerCase()
        const matchesSearch = !s || i.institutionName.toLowerCase().includes(s) || i.institutionCode.toLowerCase().includes(s)
        const matchesType = this.filterType === 'all' || i.institutionType === this.filterType
        const matchesStatus = this.filterStatus === 'all' || i.status === this.filterStatus
        return matchesSearch && matchesType && matchesStatus
      })
    },
    activePermanentCount() { return this.institutions.filter(i => i.institutionType === 'permanent' && i.status === 'active').length },
    activeTemporaryCount() { return this.institutions.filter(i => i.institutionType === 'temporary' && i.status === 'active').length },
    totalEmployees() { return this.institutions.filter(i => i.status === 'active').reduce((s, i) => s + (i.employeeCount || 0), 0) },
    alertCount() { return this.institutions.filter(i => i.status === 'frozen' || i.status === 'dissolved').length },
    transferTargets() {
      if (!this.toTransfer) return []
      return this.institutions.filter(inst => inst.institutionID !== this.toTransfer.institutionID && inst.status === 'active' && !this.isDescendant(inst.institutionID, this.toTransfer.institutionID))
    },
    mergeTargets() {
      if (!this.toMerge) return []
      return this.institutions.filter(inst => inst.institutionID !== this.toMerge.institutionID && inst.status === 'active' && inst.institutionType === this.toMerge.institutionType)
    },
    mergePreview() {
      if (!this.mergeTargetId || !this.toMerge) return ''
      const t = this.institutions.find(x => x.institutionID === this.mergeTargetId)
      if (!t) return ''
      return `目标机构：${t.institutionName} (${t.institutionCode}) • 人员合计：${t.employeeCount} + ${this.toMerge.employeeCount} = ${t.employeeCount + this.toMerge.employeeCount} 人 • \"${this.toMerge.institutionName}\" 将设为已合并`
    },
    hasSubOfMerge() {
      if (!this.toMerge) return false
      return this.institutions.some(inst => inst.parentInstitutionID === this.toMerge.institutionID)
    }
  },
  created() {
    // 优先拉取后端数据，失败回退示例数据
    this.fetchList()
  },
  methods: {
    openCreate() { this.selectedInstitution = null; this.formVisible = true },
    onEdit(row) { this.selectedInstitution = row; this.formVisible = true },
    onViewHistory(row) { this.selectedInstitution = row; this.historyVisible = true },
    onDelete(row) { this.toDelete = row; this.deleteVisible = true },
    onTransfer(row) { this.toTransfer = row; this.transferTargetId = ''; this.transferReason = ''; this.transferVisible = true },
    onMerge(row) { this.toMerge = row; this.mergeTargetId = ''; this.mergeReason = ''; this.mergeVisible = true },
    onSaveCodeRule(rule) { this.$message.success('编码规则已保存'); console.log('code rule', rule) },
    statusTagType(s) { if (s === 'active') return 'success'; if (s === 'frozen') return 'warning'; if (s === 'dissolved') return 'danger'; return '' },
    statusLabel(s) { return { active: '正常', frozen: '冻结', dissolved: '已撤销', merged: '已合并' }[s] || s },
    levelColor(level) { return level === 1 ? 'level-blue' : level === 2 ? 'level-green' : 'level-purple' },
    // 递归判断是否为子机构
    isDescendant(id, ancestor) {
      const inst = this.institutions.find(i => i.institutionID === id)
      if (!inst || !inst.parentInstitutionID) return false
      if (inst.parentInstitutionID === ancestor) return true
      return this.isDescendant(inst.parentInstitutionID, ancestor)
    },
    confirmDelete() {
      if (!this.toDelete) return
      const hasSub = this.institutions.some(i => i.parentInstitutionID === this.toDelete.institutionID)
      if (hasSub || (this.toDelete.employeeCount || 0) > 0) {
        this.$message.error('该机构下还有下属机构或在职员工，无法直接撤销')
        return
      }
      this.institutions = this.institutions.map(inst => inst.institutionID === this.toDelete.institutionID ? { ...inst, status: 'dissolved', withdrawDate: this.today() } : inst)
      this.deleteVisible = false; this.toDelete = null
    },
    confirmTransfer() {
      if (!this.toTransfer || !this.transferTargetId) return
      const apply = (instId, newParentId, newLevel, targetName) => {
        const children = this.institutions.filter(x => x.parentInstitutionID === instId)
        this.institutions = this.institutions.map(inst => {
          if (inst.institutionID === instId) {
            return { ...inst, parentInstitutionID: newParentId, parentName: newParentId ? targetName : null, level: newLevel, updateTime: this.now(), version: (parseFloat(inst.version || '1.0') + 0.1).toFixed(1), reasonForChange: this.transferReason || '机构划转', solidifiedDataDescription: `原隶属关系: ${inst.parentName || '无'} → ${targetName || '根机构'}` }
          }
          return inst
        })
        children.forEach(c => apply(c.institutionID, c.parentInstitutionID, newLevel + 1, targetName))
      }
      if (this.transferTargetId === 'root') {
        apply(this.toTransfer.institutionID, null, 1, null)
      } else {
        const t = this.institutions.find(x => x.institutionID === this.transferTargetId)
        if (t) apply(this.toTransfer.institutionID, t.institutionID, (t.level || 1) + 1, t.institutionName)
      }
      this.transferVisible = false; this.toTransfer = null; this.transferTargetId = ''; this.transferReason = ''
    },
    confirmMerge() {
      if (!this.toMerge || !this.mergeTargetId) return
      if (this.hasSubOfMerge) { this.$message.error('存在下属机构，请先处理'); return }
      if ((this.toMerge.employeeCount || 0) > 0) {
        // 弹窗确认
        this.$confirm(`该机构有 ${this.toMerge.employeeCount} 名在职人员，合并后将自动迁移到目标机构，是否继续？`, '提示', { type: 'warning' })
          .then(() => this.applyMerge())
          .catch(() => {})
      } else {
        this.applyMerge()
      }
    },
    applyMerge() {
      const t = this.institutions.find(x => x.institutionID === this.mergeTargetId)
      if (!t) return
      this.institutions = this.institutions.map(inst => {
        if (inst.institutionID === this.toMerge.institutionID) {
          return { ...inst, status: 'merged', withdrawDate: this.today(), updateTime: this.now(), reasonForChange: this.mergeReason || '机构合并', solidifiedDataDescription: `已合并至: ${t.institutionName} (${t.institutionCode})` }
        }
        if (inst.institutionID === t.institutionID) {
          return { ...inst, employeeCount: (inst.employeeCount || 0) + (this.toMerge.employeeCount || 0), updateTime: this.now(), version: (parseFloat(inst.version || '1.0') + 0.1).toFixed(1) }
        }
        return inst
      })
      this.mergeVisible = false; this.toMerge = null; this.mergeTargetId = ''; this.mergeReason = ''
    },
    onSave(payload) {
      if (this.selectedInstitution) {
        this.institutions = this.institutions.map(inst => inst.institutionID === this.selectedInstitution.institutionID ? { ...inst, ...payload } : inst)
      } else {
        const id = Date.now().toString()
        this.institutions = this.institutions.concat([{ ...payload, institutionID: id, createTime: this.now(), updateTime: this.now(), version: '1.0', status: 'active', level: payload.parentInstitutionID ? 2 : 1, employeeCount: 0, parentName: this.getParentName(payload.parentInstitutionID) }])
      }
      this.formVisible = false; this.selectedInstitution = null
    },
    getParentName(pid) { if (!pid) return null; const p = this.institutions.find(x => x.institutionID === pid); return p ? p.institutionName : null },
    today() { return new Date().toISOString().split('T')[0] },
    now() { return new Date().toISOString().replace('T', ' ').substr(0, 19) },
    seedData() {
      return [
        { institutionID: '1', institutionName: '杭州科技职业技术学院', institutionCode: 'HZVTC001', institutionType: 'permanent', parentInstitutionID: null, establishDate: '2015-03-01', effectiveDate: '2015-03-01', description: '学院整体管理运营，负责全院教学、科研、管理工作的统筹协调', phoneNumber: '0571-88888888', email: '<EMAIL>', officeAddress: '杭州市西湖区留和路299号', status: 'active', creatorID: 'admin', createTime: '2015-03-01 09:00:00', lastModifierID: 'admin', updateTime: '2024-01-15 10:30:00', approvalDocNumber: '浙教高〔2015〕10号', version: '1.0', level: 1, employeeCount: 1286, parentName: null },
        { institutionID: '2', institutionName: '计算机学院', institutionCode: 'HZVTC002', institutionType: 'permanent', parentInstitutionID: '1', establishDate: '2015-03-01', effectiveDate: '2015-03-01', description: '负责计算机科学与技术、软件工程、网络工程等专业的教学科研工作', phoneNumber: '0571-88888801', email: '<EMAIL>', officeAddress: '杭州市西湖区留和路299号计算机楼', status: 'active', creatorID: 'admin', createTime: '2015-03-01 09:00:00', lastModifierID: 'user001', updateTime: '2024-01-10 14:20:00', approvalDocNumber: '浙教高〔2015〕10号', version: '1.2', level: 2, employeeCount: 156, parentName: '杭州科技职业技术学院' },
        { institutionID: '3', institutionName: '机械工程学院', institutionCode: 'HZVTC003', institutionType: 'permanent', parentInstitutionID: '1', establishDate: '2015-03-01', effectiveDate: '2015-03-01', description: '负责机械设计制造及其自动化、机械电子工程等专业的教学科研工作', phoneNumber: '0571-88888802', email: '<EMAIL>', officeAddress: '杭州市西湖区留和路299号机械楼', status: 'active', creatorID: 'admin', createTime: '2015-03-01 09:00:00', lastModifierID: 'user002', updateTime: '2024-01-08 16:45:00', approvalDocNumber: '浙教高〔2015〕10号', version: '1.1', level: 2, employeeCount: 89, parentName: '杭州科技职业技术学院' },
        { institutionID: '4', institutionName: 'AI技术创新项目组', institutionCode: 'HZVTC004', institutionType: 'temporary', parentInstitutionID: '2', establishDate: '2024-01-01', effectiveDate: '2024-01-01', withdrawDate: '2024-12-31', description: '负责人工智能技术研发与创新，推动AI技术在教学科研中的应用', phoneNumber: '0571-88888803', email: '<EMAIL>', officeAddress: '杭州市西湖区留和路299号计算机楼301', status: 'active', creatorID: 'user001', createTime: '2024-01-01 09:00:00', lastModifierID: 'user001', updateTime: '2024-01-01 09:00:00', approvalDocNumber: '院发〔2024〕1号', version: '1.0', level: 3, employeeCount: 12, parentName: '计算机学院' },
        { institutionID: '5', institutionName: '软件工程系', institutionCode: 'HZVTC005', institutionType: 'permanent', parentInstitutionID: '2', establishDate: '2015-09-01', effectiveDate: '2015-09-01', description: '负责软件工程专业的教学管理、实践教学和产学研合作', phoneNumber: '0571-88888804', email: '<EMAIL>', officeAddress: '杭州市西湖区留和路299号计算机楼201', status: 'active', creatorID: 'user001', createTime: '2015-09-01 09:00:00', lastModifierID: 'user003', updateTime: '2024-01-05 11:15:00', approvalDocNumber: '院发〔2015〕15号', version: '1.3', level: 3, employeeCount: 45, parentName: '计算机学院' },
        { institutionID: '6', institutionName: '网络技术系', institutionCode: 'HZVTC006', institutionType: 'permanent', parentInstitutionID: '2', establishDate: '2015-09-01', effectiveDate: '2015-09-01', description: '负责网络工程、计算机网络技术等专业的教学和技术服务', phoneNumber: '0571-88888805', email: '<EMAIL>', officeAddress: '杭州市西湖区留和路299号计算机楼202', status: 'active', creatorID: 'user001', createTime: '2015-09-01 09:00:00', lastModifierID: 'user004', updateTime: '2024-01-03 09:30:00', approvalDocNumber: '院发〔2015〕15号', version: '1.2', level: 3, employeeCount: 38, parentName: '计算机学院' },
        { institutionID: '7', institutionName: '机械制造系', institutionCode: 'HZVTC007', institutionType: 'permanent', parentInstitutionID: '3', establishDate: '2015-09-01', effectiveDate: '2015-09-01', description: '负责机械制造与自动化、数控技术等专业的教学实训工作', phoneNumber: '0571-88888806', email: '<EMAIL>', officeAddress: '杭州市西湖区留和路299号机械楼101', status: 'active', creatorID: 'user002', createTime: '2015-09-01 09:00:00', lastModifierID: 'user005', updateTime: '2024-01-02 15:20:00', approvalDocNumber: '院发〔2015〕16号', version: '1.1', level: 3, employeeCount: 32, parentName: '机械工程学院' },
        { institutionID: '8', institutionName: '自动化系', institutionCode: 'HZVTC008', institutionType: 'permanent', parentInstitutionID: '3', establishDate: '2015-09-01', effectiveDate: '2015-09-01', description: '负责电气自动化、工业机器人等专业的教学和技术开发', phoneNumber: '0571-88888807', email: '<EMAIL>', officeAddress: '杭州市西湖区留和路299号机械楼102', status: 'active', creatorID: 'user002', createTime: '2015-09-01 09:00:00', lastModifierID: 'user006', updateTime: '2023-12-28 13:45:00', approvalDocNumber: '院发〔2015〕16号', version: '1.0', level: 3, employeeCount: 28, parentName: '机械工程学院' }
      ]
    },
    async fetchList() {
      try {
        const res = await instApi.list()
        // 兼容后端不同字段命名（这里假设后端直接返回数组或 { content: [], data: [] }）
        const arr = Array.isArray(res) ? res : (res && (res.content || res.data)) || []
        if (arr && arr.length) {
          this.institutions = arr
          return
        }
        this.institutions = this.seedData()
      } catch (e) {
        console.warn('加载机构列表失败，回退示例数据', e)
        this.institutions = this.seedData()
      }
    }
  }
}
</script>

<style scoped>
.institution-page { display: flex; flex-direction: column; gap: 16px; }
.page-header { display: flex; align-items: center; justify-content: space-between; }
.page-header h2 { margin: 0; line-height: 1.3; }
.sub { color: #909399; margin-top: 6px; }
.actions > .el-button + .el-button { margin-left: 8px; }
.stat { display: flex; align-items: center; justify-content: space-between; }
.icon { font-size: 28px; }
.icon.blue { color: #409EFF; }
.icon.purple { color: #A66EFB; }
.icon.green { color: #67C23A; }
.icon.orange { color: #E6A23C; }
.muted { color: #909399; margin-left: 8px; }
.mb16 { margin-bottom: 16px; }
.mb8 { margin-bottom: 8px; }
.view-toggle { text-align: right; }
.card-header { display: flex; align-items: center; justify-content: space-between; padding: 4px 0; }
.card-header .count { margin-left: 8px; color: #909399; font-weight: normal; }
.level-bar { display: inline-block; width: 4px; height: 16px; margin-right: 6px; border-radius: 2px; background: #909399; }
.level-blue { background: #409EFF; }
.level-green { background: #67C23A; }
.level-purple { background: #A66EFB; }
.mt8 { margin-top: 8px; }
.mono { font-family: Menlo, Monaco, Consolas, 'Courier New', monospace; }
.hint { background: #f5f7fa; padding: 8px 12px; border-radius: 6px; margin-bottom: 8px; }
.hint .row { margin: 2px 0; }
.orange { color: #E6A23C; }

/* 移动端小屏优化 */
@media (max-width: 768px) {
  .mb8-sm { margin-bottom: 8px; }
  .mt8-sm { margin-top: 8px; }
  .view-toggle { text-align: left; }
}
</style>

