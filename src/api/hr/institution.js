import request from '@/utils/request'

// 机构管理 API（示例，按后端实际路由调整）
// 统一前缀建议：/api/institution

export function list(params) {
  return request({ url: 'api/institution', method: 'get', params })
}

export function get(id) {
  return request({ url: `api/institution/${id}`, method: 'get' })
}

export function add(data) {
  return request({ url: 'api/institution', method: 'post', data })
}

export function edit(data) {
  return request({ url: 'api/institution', method: 'put', data })
}

export function del(ids) {
  return request({ url: 'api/institution', method: 'delete', data: ids })
}

// 划转
export function transfer(data) {
  // { institutionId, targetId, reason }
  return request({ url: 'api/institution/transfer', method: 'post', data })
}

// 合并
export function merge(data) {
  // { fromId, toId, reason }
  return request({ url: 'api/institution/merge', method: 'post', data })
}

export default { list, get, add, edit, del, transfer, merge }

